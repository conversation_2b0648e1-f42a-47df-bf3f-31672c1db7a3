<div class="min-h-screen bg-white flex flex-col">

  <!-- Header sa login page, same design sa landing page pero naa pay "Back to Home" button. Gi-flex nako ni para ma-responsive ug naa pay logo sa left side -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10 flex-shrink-0">
    <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
      <div class="flex-1 flex justify-start items-center">
        <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
          <img
            src="assets/images/BcLogo.png"
            alt="Benedicto College Logo"
            class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
            onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
            onload="console.log('Logo loaded successfully:', this.src);"
          >
        </a>
      </div>
      <nav class="flex-shrink-0">
        <button
          routerLink="/"
          class="bg-black hover:bg-gray-800 text-white font-semibold py-2 px-4 sm:px-6 rounded-lg transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-sm sm:text-base flex items-center space-x-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          <span>Back to Home</span>
        </button>
      </nav>
    </div>
  </header>

  <!-- Responsive layout: IMAGE LOGO | LOGIN FORM (desktop) | LOGIN FORM only (mobile) -->
  <main class="flex-1 flex">

    <!-- IMAGE LOGO - Hidden below 1000px, visible on 1000px and above -->
    <img
      src="assets/images/login-logo.png"
      alt="Benedicto College Login"
      class="login-logo-container"
      style="max-height: 300px; max-width: 80%; height: auto; object-fit: contain; margin: auto; display: block;"
      onerror="this.src='assets/images/login-logo.jpg'; console.log('Switched to JPG format');"
      onload="console.log('Login logo loaded successfully');"
    >

    <!-- LOGIN FORM - Full width below 1000px, 60% width on 1000px and above -->
    <div class="login-form-container flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <!-- Login card container, gi-design nako ni nga professional ug nindot. Naa pay shadow effects ug rounded corners para modern tan-awon -->
      <div class="bg-white rounded-2xl shadow-xl border-2 border-gray-300 p-5 w-full max-w-sm relative overflow-hidden">
            <!-- Decorative background elements sa login card, gi-butang nako ni para naa pay visual interest pero dili overwhelming sa form -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-100 to-orange-200 rounded-full opacity-50 -translate-y-16 translate-x-16"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-50 translate-y-12 -translate-x-12"></div>

            <!-- User avatar section sa top sa login form, gi-design nako ni nga nindot ug professional. Naa pay welcome message para friendly ang feel -->
            <div class="text-center mb-6 sm:mb-8 relative z-10">
              <div class="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-xl">
                <svg class="w-8 h-8 sm:w-10 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Welcome Back!</h2>
              <p class="text-sm sm:text-base text-gray-600">Access your academic resources</p>
            </div>

            <!-- Main login form, diri nako gi-butang ang student ID ug password fields. Naa pay validation ug error handling para secure ug user-friendly -->
            <form class="space-y-3 relative z-10" id="loginForm">
              <!-- Student ID input field, gi-set nako ni nga required ug naa pay specific format validation (2000-00000). Naa pay error message kung mali ang format -->
              <div>
                <label for="studentId" class="block text-sm font-semibold text-gray-700 mb-2">
                  Student ID <span class="text-red-500">*</span>
                </label>
            <input
              type="text"
              id="studentId"
              name="studentId"
              placeholder="2000-00000"
              pattern="[0-9]{4}-[0-9]{5}"
              maxlength="10"
              class="w-full px-3 py-2 sm:px-4 sm:py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300 bg-white text-gray-900 placeholder-gray-500 text-sm sm:text-base"
              required
              oninput="formatStudentId(this); validateStudentId(this)"
              onkeydown="return handleStudentIdKeydown(event)"
              onpaste="return false"
              ondrop="return false"
            >
            <div id="studentIdError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium hidden bg-red-50 border border-red-200 rounded-lg p-2">
              ⚠️ Student ID must be exactly 9 digits (format: 2000-00000)
            </div>
          </div>

          <!-- Password input field, gi-set nako ni nga required ug naa pay validation. Gi-hide nako ang password para secure -->
          <div>
            <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
              Password <span class="text-red-500">*</span>
            </label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="••••••••••"
              class="w-full px-3 py-2 sm:px-4 sm:py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300 bg-white text-gray-900 placeholder-gray-500 text-sm sm:text-base"
              required
              oninput="validatePassword(this)"
            >
            <div id="passwordError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium hidden bg-red-50 border border-red-200 rounded-lg p-2">
              ⚠️ Password is required
            </div>
          </div>

          <!-- Remember me checkbox ug forgot password link, gi-layout nako ni nga responsive para nindot sa mobile ug desktop -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <label class="flex items-center">
              <input
                type="checkbox"
                class="w-4 h-4 text-orange-500 border-2 border-gray-300 rounded focus:ring-orange-200 focus:ring-2 bg-white"
              >
              <span class="ml-3 text-sm font-medium text-gray-700">Remember me</span>
            </label>
            <a href="#" class="text-sm font-semibold text-orange-600 hover:text-orange-700 transition duration-300">
              Forgot your password?
            </a>
          </div>

          <!-- Login button nga nindot kaayo, gi-gradient nako ang background ug naa pay hover effects. Naa pay form validation function nga ma-trigger kung i-click -->
          <button
            type="submit"
            class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 sm:py-4 px-4 sm:px-6 rounded-xl transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-blue-200 text-sm sm:text-base"
            onclick="validateForm(event)"
          >
            Sign In to Your Account
          </button>
        </form>

            <!-- Help links section sa bottom sa form, para sa mga users nga need og assistance. Gi-responsive nako ni para nindot sa tanan devices -->
            <div class="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200 text-center relative z-10">
              <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm">
                <a href="#" class="text-gray-600 hover:text-orange-600 font-medium transition duration-300">
                  Need Help?
                </a>
                <span class="text-gray-400 hidden sm:inline">|</span>
                <a href="#" class="text-gray-600 hover:text-orange-600 font-medium transition duration-300">
                  Request Account Access
                </a>
              </div>
        </div>
      </div>
    </div>

  </main>

  <!-- Footer sa login page, same design sa landing page. Naa pay copyright information ug consistent ang orange borders para sa branding -->
  <footer class="bg-black py-4 sm:py-6 lg:py-8 border-t-8 border-orange-500 border-b-8 border-b-orange-500 relative z-10 mt-auto">
    <div class="container mx-auto px-4 sm:px-6">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        <div class="text-gray-200 text-sm sm:text-base text-center md:text-left">
          &copy; 2025 Library Management System. All Rights Reserved.
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-center">
          <a routerLink="/privacy-policy" class="text-gray-400 hover:text-orange-400 transition duration-300 text-sm font-medium">Privacy Policy</a>
          <a routerLink="/terms-of-service" class="text-gray-400 hover:text-orange-400 transition duration-300 text-sm font-medium">Terms of Service</a>
          <a routerLink="/support" class="text-gray-400 hover:text-orange-400 transition duration-300 text-sm font-medium">Support</a>
        </div>
      </div>
    </div>
  </footer>
</div>

<script>
  // Handle keydown events for Student ID input - only allow numbers
  function handleStudentIdKeydown(event) {
    const input = event.target;
    const key = event.key;
    const currentValue = input.value;

    // Allow control keys (backspace, delete, tab, escape, enter, arrows)
    if (['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(key)) {
      return true;
    }

    // Only allow numbers 0-9
    if (!/^[0-9]$/.test(key)) {
      event.preventDefault();
      return false;
    }

    // Don't allow more than 9 digits total
    const digitsOnly = currentValue.replace(/\D/g, '');
    if (digitsOnly.length >= 9) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  // Format Student ID input with dash
  function formatStudentId(input) {
    // Remove all non-digit characters
    let value = input.value.replace(/\D/g, '');

    // Limit to 9 digits maximum
    if (value.length > 9) {
      value = value.substring(0, 9);
    }

    // Add dash after 4 digits
    if (value.length >= 4) {
      value = value.substring(0, 4) + '-' + value.substring(4, 9);
    }

    input.value = value;
  }

  // Validate Student ID format
  function validateStudentId(input) {
    const pattern = /^[0-9]{4}-[0-9]{5}$/;
    const errorDiv = document.getElementById('studentIdError');

    if (input.value && !pattern.test(input.value)) {
      input.classList.add('border-red-500');
      input.classList.remove('border-gray-200');
      errorDiv.classList.remove('hidden');
    } else {
      input.classList.remove('border-red-500');
      input.classList.add('border-gray-200');
      errorDiv.classList.add('hidden');
    }
  }

  // Validate Password
  function validatePassword(input) {
    const errorDiv = document.getElementById('passwordError');

    if (input.value.trim() === '') {
      input.classList.add('border-red-500');
      input.classList.remove('border-gray-200');
      errorDiv.classList.remove('hidden');
    } else {
      input.classList.remove('border-red-500');
      input.classList.add('border-gray-200');
      errorDiv.classList.add('hidden');
    }
  }

  // Validate entire form on submit
  function validateForm(event) {
    event.preventDefault();

    const studentId = document.getElementById('studentId');
    const password = document.getElementById('password');
    const pattern = /^[0-9]{4}-[0-9]{5}$/;

    let isValid = true;

    // Validate Student ID
    if (!studentId.value || !pattern.test(studentId.value)) {
      validateStudentId(studentId);
      isValid = false;
    }

    // Validate Password
    if (!password.value.trim()) {
      validatePassword(password);
      isValid = false;
    }

    if (isValid) {
      // Form is valid, you can submit here
      alert('Form is valid! Ready to submit.');
      // In a real application, you would submit the form to your backend
      // document.getElementById('loginForm').submit();
    } else {
      // Show general error message
      alert('Please correct the errors above before submitting.');
    }
  }

  // Allow only numbers in Student ID field
  function isNumberKey(evt) {
    var charCode = (evt.which) ? evt.which : evt.keyCode;

    // Allow backspace (8), delete (46), tab (9), escape (27), enter (13), arrow keys (37-40)
    if (charCode == 8 || charCode == 9 || charCode == 27 || charCode == 13 ||
        charCode == 37 || charCode == 38 || charCode == 39 || charCode == 40 || charCode == 46) {
      return true;
    }

    // Allow numbers only (48-57 for regular keys, 96-105 for numpad)
    if ((charCode >= 48 && charCode <= 57) || (charCode >= 96 && charCode <= 105)) {
      return true;
    }

    // Block everything else
    evt.preventDefault();
    return false;
  }

  // Additional input sanitization
  document.addEventListener('DOMContentLoaded', function() {
    const studentIdInput = document.getElementById('studentId');

    // Prevent paste of non-numeric content
    studentIdInput.addEventListener('paste', function(e) {
      e.preventDefault();
      const paste = (e.clipboardData || window.clipboardData).getData('text');
      const numericOnly = paste.replace(/\D/g, '');
      if (numericOnly) {
        this.value = numericOnly;
        formatStudentId(this);
        validateStudentId(this);
      }
    });

    // Prevent drag and drop
    studentIdInput.addEventListener('drop', function(e) {
      e.preventDefault();
    });

    // Additional input event to catch any remaining non-numeric characters
    studentIdInput.addEventListener('input', function(e) {
      const cursorPosition = this.selectionStart;
      const oldValue = this.value;

      // Remove any non-numeric characters except dash
      let newValue = this.value.replace(/[^0-9-]/g, '');

      // If value changed, update it
      if (newValue !== oldValue) {
        this.value = newValue;
        // Restore cursor position
        this.setSelectionRange(cursorPosition, cursorPosition);
      }
    });
  });
</script>
