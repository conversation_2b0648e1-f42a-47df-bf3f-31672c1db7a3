<div class="min-h-screen bg-white flex flex-col">

  <!-- header with back button -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10 flex-shrink-0">
    <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
      <div class="flex-1 flex justify-start items-center">
        <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
          <img
            src="assets/images/BcLogo.png"
            alt="Benedicto College Logo"
            class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
            onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
            onload="console.log('Logo loaded successfully:', this.src);"
          >
        </a>
      </div>
      <nav class="flex-shrink-0">
        <button
          routerLink="/"
          class="bg-black hover:bg-gray-800 text-white font-semibold py-2 px-4 sm:px-6 rounded-lg transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-sm sm:text-base flex items-center space-x-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          <span>Back to Home</span>
        </button>
      </nav>
    </div>
  </header>

  <!-- main content -->
  <main class="flex-1 flex">

    <!-- logo image -->
    <div class="flex items-center justify-center p-8">
      <img
        src="assets/images/login-logo.png"
        alt="Benedicto College Login"
        class="max-w-full h-auto object-fit-contain"
        style="max-height: 500px;"
        onerror="this.src='assets/images/login-logo.jpg'; console.log('Switched to JPG format');"
        onload="console.log('Login logo loaded successfully');"
      >
    </div>

    <!-- login form container -->
    <div class="flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <!-- login card -->
      <div class="bg-white rounded-2xl shadow-xl border-2 border-gray-300 p-5 w-full max-w-sm mx-auto relative overflow-hidden">
            <!-- decorative circles lang -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-100 to-orange-200 rounded-full opacity-50 -translate-y-16 translate-x-16"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-50 translate-y-12 -translate-x-12"></div>

            <!-- user icon ug welcome text -->
            <div class="text-center mb-6 sm:mb-8 relative z-10">
             <div class="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-gray-800 to-gray-900 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-xl">
  <svg class="w-8 h-8 sm:w-10 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
  </svg>
</div>

              <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Welcome Back!</h2>
              <p class="text-sm sm:text-base text-gray-600">Access your academic resources</p>
            </div>

            <!-- login form -->
            <form class="space-y-3 relative z-10" id="loginForm">
              <!-- student ID field -->
              <div>
                <label for="studentId" class="block text-sm font-semibold text-gray-700 mb-2">
                  Student ID <span class="text-red-500">*</span>
                </label>
<input
  type="text"
  id="studentId"
  name="studentId"
  placeholder="2000-00000"
  maxlength="11"
  required
  (keydown)="blockNonNumericKeys($event)"
  (input)="formatStudentId($event)"
  class="w-full px-3 py-2 sm:px-4 sm:py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300 bg-white text-gray-900 placeholder-gray-500 text-sm sm:text-base"
/>



            <div id="studentIdError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium hidden bg-red-50 border border-red-200 rounded-lg p-2">
              ⚠️ Student ID must be exactly 9 digits (format: 2000-00000)
            </div>
          </div>

          <!-- password field -->
          <div>
            <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
              Password <span class="text-red-500">*</span>
            </label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="••••••••••"
              class="w-full px-3 py-2 sm:px-4 sm:py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300 bg-white text-gray-900 placeholder-gray-500 text-sm sm:text-base"
              required
              oninput="validatePassword(this)"
              onkeydown="return blockNonNumbers(event)"
            >
            <div id="passwordError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium hidden bg-red-50 border border-red-200 rounded-lg p-2">
              ⚠️ Password is required
            </div>
          </div>

          <!-- remember me ug forgot password -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <label class="flex items-center">
              <input
                type="checkbox"
                class="w-4 h-4 text-orange-500 border-2 border-gray-300 rounded focus:ring-orange-200 focus:ring-2 bg-white"
              >
              <span class="ml-3 text-sm font-medium text-gray-700">Remember me</span>
            </label>
            <a href="#" class="text-sm font-semibold text-orange-600 hover:text-orange-700 transition duration-300">
              Forgot your password?
            </a>
          </div>

          <!-- login button -->
          <button
            type="submit"
            class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 sm:py-4 px-4 sm:px-6 rounded-xl transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-blue-200 text-sm sm:text-base"
            onclick="validateForm(event)"
          >
            Sign In to Your Account
          </button>
        </form>

            <!-- help links -->
            <div class="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200 text-center relative z-10">
              <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm">
                <a href="#" class="text-gray-600 hover:text-orange-600 font-medium transition duration-300">
                  Need Help?
                </a>
                <span class="text-gray-400 hidden sm:inline">|</span>
                <a href="#" class="text-gray-600 hover:text-orange-600 font-medium transition duration-300">
                  Request Account Access
                </a>
              </div>
        </div>
      </div>
    </div>

  </main>

  <!-- Footer sa login page, same design sa landing page. Naa pay copyright information ug consistent ang orange borders para sa branding -->


<footer _ngcontent-ng-c4113940903="" class="bg-black py-8 border-t-8 border-orange-500 border-b-0 border-b-orange-500">
     <div _ngcontent-ng-c4113940903="" class="container mx-auto px-6">
        <div _ngcontent-ng-c4113940903="" class="flex flex-col md:flex-row justify-between items-center">
            <div _ngcontent-ng-c4113940903="" class="text-gray-200 mb-4 md:mb-0"> © 2025 Library Management System. All Rights Reserved. 
              </div>
                  <div _ngcontent-ng-c4113940903="" class="flex space-x-6">
                   <a _ngcontent-ng-c4113940903="" href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">
                    Privacy Policy
                </a>
             <a _ngcontent-ng-c4113940903="" href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">
              Terms of Service
             </a>
             <a _ngcontent-ng-c4113940903="" href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">
              Support
            </a>
          </div>
        </div>
      </div>
    </footer>
</div>

<script>
  // Handle Student ID input formatting (auto dash after 4 digits)
  function handleStudentIdInput(input) {
    let value = input.value.replace(/\D/g, ''); // Keep only digits

    if (value.length > 9) {
      value = value.slice(0, 9); // Max of 9 digits
    }

    // Insert dash after the 4th digit
    if (value.length > 4) {
      input.value = value.slice(0, 4) + '-' + value.slice(4);
    } else {
      input.value = value;
    }
  }

  // Validate Student ID pattern: ####-#####
  function validateStudentId(input) {
    const pattern = /^[0-9]{4}-[0-9]{5}$/;
    const errorDiv = document.getElementById('studentIdError');

    if (input.value && !pattern.test(input.value)) {
      input.classList.add('border-red-500');
      input.classList.remove('border-gray-200');
      errorDiv.classList.remove('hidden');
    } else {
      input.classList.remove('border-red-500');
      input.classList.add('border-gray-200');
      errorDiv.classList.add('hidden');
    }
  }

  // Validate Password (not empty)
  function validatePassword(input) {
    const errorDiv = document.getElementById('passwordError');

    if (input.value.trim() === '') {
      input.classList.add('border-red-500');
      input.classList.remove('border-gray-200');
      errorDiv.classList.remove('hidden');
    } else {
      input.classList.remove('border-red-500');
      input.classList.add('border-gray-200');
      errorDiv.classList.add('hidden');
    }
  }

  // Form submit validation
  function validateForm(event) {
    event.preventDefault();

    const studentId = document.getElementById('studentId');
    const password = document.getElementById('password');
    const pattern = /^[0-9]{4}-[0-9]{5}$/;

    let isValid = true;

    if (!studentId.value || !pattern.test(studentId.value)) {
      validateStudentId(studentId);
      isValid = false;
    }

    if (!password.value.trim()) {
      validatePassword(password);
      isValid = false;
    }

    if (isValid) {
      alert('Form is valid! Ready to submit.');
      // document.getElementById('loginForm').submit(); // Uncomment to actually submit
    } else {
      alert('Please correct the errors above before submitting.');
    }
  }

  // Setup input restrictions and live formatting
  document.addEventListener('DOMContentLoaded', function () {
    const studentIdInput = document.getElementById('studentId');

    // Restrict to only number keys
    studentIdInput.addEventListener('keydown', function (e) {
      const key = e.key;

      if (
        key === "Backspace" || key === "Delete" || key === "Tab" ||
        key === "Escape" || key === "Enter" || key.startsWith("Arrow")
      ) {
        return;
      }

      // Block if not a digit
      if (!/^[0-9]$/.test(key)) {
        e.preventDefault();
      }
    });

    // Format and validate on input
    studentIdInput.addEventListener('input', function () {
      handleStudentIdInput(this);
      validateStudentId(this);
    });

    // Prevent paste of non-numeric content
    studentIdInput.addEventListener('paste', function (e) {
      e.preventDefault();
      const paste = (e.clipboardData || window.clipboardData).getData('text');
      const numericOnly = paste.replace(/\D/g, '').slice(0, 9);
      if (numericOnly) {
        this.value = numericOnly;
        handleStudentIdInput(this);
        validateStudentId(this);
      }
    });

    // Prevent drag and drop input
    studentIdInput.addEventListener('drop', function (e) {
      e.preventDefault();
    });
  });
</script>
