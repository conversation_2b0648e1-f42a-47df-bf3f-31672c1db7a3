import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [RouterModule],
  templateUrl: './login.html',
  styleUrl: './login.css'
})
export class LoginComponent {

}

blockNonNumericKeys(event: KeyboardEvent): void {
  const allowedKeys = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete'];

  if (
    allowedKeys.includes(event.key) ||
    /^[0-9]$/.test(event.key) // only digits allowed
  ) {
    return; // allow
  }

  event.preventDefault(); // block everything else
}

formatStudentId(event: Event): void {
  const input = event.target as HTMLInputElement;
  let value = input.value.replace(/\D/g, '').slice(0, 9); // max 9 digits

  if (value.length > 4) {
    input.value = `${value.slice(0, 4)}-${value.slice(4)}`;
  } else {
    input.value = value;
  }
}


