/* Login Page Styles - Benedicto College Library Management System */

/* Z-index layering for proper element stacking */
.z-0 {
  z-index: 0;
}

.z-5 {
  z-index: 5;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

/* Login logo container styling for better visibility */
.login-logo-container {
  position: relative;
  z-index: 50;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Ensure login logo image is always visible */
.login-logo-image {
  position: relative;
  z-index: 60;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  min-height: 300px;
  max-height: 500px;
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Fallback styling for image loading issues */
.login-logo-image[src=""],
.login-logo-image:not([src]) {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.5rem;
}

.login-logo-image[src=""]:before,
.login-logo-image:not([src]):before {
  content: "Benedicto College Logo";
}

/* Enhanced text visibility */
.login-welcome-text {
  position: relative;
  z-index: 30;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Form container z-index */
.login-form-container {
  position: relative;
  z-index: 25;
}

/* Background elements should stay behind */
.login-background {
  position: absolute;
  z-index: 0;
}

.login-decorative {
  position: absolute;
  z-index: 5;
  pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .login-logo-container {
    display: none;
  }
}

/* Ensure proper stacking context */
.login-main-content {
  position: relative;
  z-index: 15;
}

/* Header should be above everything */
.login-header {
  position: relative;
  z-index: 100;
}